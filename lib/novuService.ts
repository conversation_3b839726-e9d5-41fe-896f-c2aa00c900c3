import { Platform } from 'react-native';
import * as Device from 'expo-device';
import Toast from 'react-native-toast-message';

// Novu configuration
export const NOVU_CONFIG = {
  applicationIdentifier: process.env.EXPO_PUBLIC_NOVU_APP_ID || '',
  backendUrl: process.env.EXPO_PUBLIC_NOVU_BACKEND_URL || 'https://api.novu.co',
};

export interface NotificationPermissionStatus {
  status: 'granted' | 'denied' | 'undetermined';
}

export interface PushTokenData {
  token: string;
  type: 'expo' | 'fcm' | 'apns';
}

/**
 * Novu-based notification service
 * Handles permission requests, token management, and notification setup
 */
export class NovuNotificationService {
  private static instance: NovuNotificationService;
  
  public static getInstance(): NovuNotificationService {
    if (!NovuNotificationService.instance) {
      NovuNotificationService.instance = new NovuNotificationService();
    }
    return NovuNotificationService.instance;
  }

  /**
   * Check current notification permission status
   */
  async checkPermissionStatus(): Promise<NotificationPermissionStatus> {
    try {
      // For now, we'll use a simple check - this can be enhanced with actual permission APIs
      if (Platform.OS === 'ios') {
        // On iOS, we can check notification settings
        return { status: 'undetermined' };
      } else {
        // On Android, notifications are generally allowed by default
        return { status: 'granted' };
      }
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      return { status: 'denied' };
    }
  }

  /**
   * Request notification permissions from the user
   */
  async requestPermissions(): Promise<NotificationPermissionStatus> {
    try {
      if (!Device.isDevice) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Must use physical device for Push Notifications',
        });
        return { status: 'denied' };
      }

      // For now, we'll simulate permission request
      // In a real implementation, you'd use the appropriate permission APIs
      return { status: 'granted' };
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to request notification permissions',
      });
      return { status: 'denied' };
    }
  }

  /**
   * Get push token for the device
   * This will be used to register the device with Novu
   */
  async getPushToken(): Promise<PushTokenData | null> {
    try {
      if (!Device.isDevice) {
        console.warn('Push tokens are only available on physical devices');
        return null;
      }

      // For now, we'll return a placeholder token
      // In a real implementation, you'd get the actual FCM/APNS token
      const token = `novu_token_${Platform.OS}_${Date.now()}`;
      
      return {
        token,
        type: Platform.OS === 'ios' ? 'apns' : 'fcm',
      };
    } catch (error) {
      console.error('Error getting push token:', error);
      return null;
    }
  }

  /**
   * Register device with Novu for push notifications
   */
  async registerDevice(subscriberId: string, pushToken: PushTokenData): Promise<boolean> {
    try {
      // This would typically make an API call to your backend
      // which then registers the device with Novu
      console.log('Registering device with Novu:', {
        subscriberId,
        pushToken,
      });

      // Simulate successful registration
      return true;
    } catch (error) {
      console.error('Error registering device with Novu:', error);
      return false;
    }
  }

  /**
   * Update notification settings on the backend
   */
  async updateNotificationSettings(
    token: string,
    settings: {
      mobile: boolean;
      email: boolean;
      subscriberId: string;
      pushToken?: PushTokenData;
    }
  ): Promise<boolean> {
    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/user/update-notifications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          mobile: settings.mobile,
          email: settings.email,
          subscriberId: settings.subscriberId,
          novuPushToken: settings.pushToken?.token,
          pushTokenType: settings.pushToken?.type,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Notification settings updated:', data);

      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Notification Settings Updated',
      });

      return true;
    } catch (error) {
      console.error('Error updating notification settings:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to update notification settings',
      });
      return false;
    }
  }

  /**
   * Setup notification channels for Android
   */
  async setupNotificationChannels(): Promise<void> {
    try {
      if (Platform.OS === 'android') {
        // Setup default notification channel
        console.log('Setting up Android notification channels');
        // This would typically configure notification channels
        // For now, we'll just log the setup
      }
    } catch (error) {
      console.error('Error setting up notification channels:', error);
    }
  }

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    try {
      await this.setupNotificationChannels();
      console.log('Novu notification service initialized');
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  }
}

// Export singleton instance
export const novuNotificationService = NovuNotificationService.getInstance();
