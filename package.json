{"name": "allyson", "version": "2.0.0", "main": "expo-router/entry", "scripts": {"eas-build-pre-install": "npm config set legacy-peer-deps true", "postinstall": "patch-package", "start:local": "NODE_ENV=development expo start -c", "start:preview": "NODE_ENV=test expo start -c", "start:production": "NODE_ENV=production expo start -c", "android": "expo run:android", "ios": "expo run:ios", "eas:build:development:android": "eas build --platform android --profile development", "eas:build:development:ios": "eas build --platform ios --profile development", "eas:build:preview:android": "eas build --platform android --profile preview", "eas:build:preview:ios": "eas build --platform ios --profile preview", "eas:build:production:android": "eas build --platform android --profile production", "eas:build:production:ios": "eas build --platform ios --profile production", "start": "expo start", "convert-to-tsx": "./convert-to-tsx.sh"}, "dependencies": {"@clerk/clerk-expo": "^1.2.0", "@convex-dev/auth": "^0.0.87", "@gorhom/bottom-sheet": "^4.6.1", "@hookform/resolvers": "^3.3.4", "@legendapp/motion": "^2.2.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-portal": "^1.0.4", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-toolbar": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/slider": "4.5.6", "@react-navigation/drawer": "^6.6.7", "@react-navigation/material-top-tabs": "^6.6.6", "@react-navigation/native": "^6.1.10", "@react-navigation/stack": "^6.3.20", "@rn-primitives/accordion": "^1.1.0", "@rn-primitives/portal": "^1.1.0", "@rn-primitives/slot": "^1.1.0", "@rn-primitives/types": "^1.1.0", "@shopify/flash-list": "1.7.6", "@superwall/react-native-superwall": "^2.1.7", "@tabler/icons-react-native": "^3.14.0", "@tanstack/react-table": "^8.12.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.8", "crypto-es": "^2.1.0", "crypto-js": "^4.2.0", "expo": "~53.0.0", "expo-application": "~6.1.4", "expo-auth-session": "~6.2.0", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.6", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.1", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.2.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-media-library": "~17.1.7", "expo-navigation-bar": "~4.2.5", "expo-quick-actions": "1.0.0", "expo-router": "~5.0.7", "expo-screen-orientation": "~8.1.7", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-store-review": "~8.1.5", "expo-system-ui": "~5.0.8", "expo-tracking-transparency": "~5.2.4", "expo-updates": "~0.28.14", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.336.0", "nativewind": "^4.1.23", "posthog-react-native": "^2.11.6", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.50.1", "react-native": "0.79.3", "react-native-adapty": "^2.11.1", "react-native-calendars": "^1.1303.0", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-display": "^7.0.2", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-sse": "^1.2.1", "react-native-svg": "15.11.2", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.2.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tabler-icons-react-native": "^3.1.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "@types/react-native": "~0.73.0", "patch-package": "^8.0.0", "tailwindcss": "^3.3.2", "typescript": "~5.8.3"}, "private": true, "resolutions": {"semver": "^7.5.2", "markdown-it": "^13.0.2", "cookie": "^0.5.0", "send": "^0.18.0"}, "overrides": {"semver": "^7.5.2", "markdown-it": "^13.0.2", "cookie": "^0.5.0", "send": "^0.18.0"}}